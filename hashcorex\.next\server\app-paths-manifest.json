{"/(auth)/login/page": "app/(auth)/login/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/(dashboard)/dashboard/page": "app/(dashboard)/dashboard/page.js", "/(public)/about/page": "app/(public)/about/page.js", "/(public)/contact/page": "app/(public)/contact/page.js", "/(public)/privacy/page": "app/(public)/privacy/page.js", "/(public)/terms/page": "app/(public)/terms/page.js", "/_not-found/page": "app/_not-found/page.js", "/admin/page": "app/admin/page.js", "/api/admin/binary-matching/manual/route": "app/api/admin/binary-matching/manual/route.js", "/api/admin/check/route": "app/api/admin/check/route.js", "/api/admin/settings/pricing/route": "app/api/admin/settings/pricing/route.js", "/api/admin/settings/route": "app/api/admin/settings/route.js", "/api/admin/stats/route": "app/api/admin/stats/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/binary-points/info/route": "app/api/binary-points/info/route.js", "/api/earnings/route": "app/api/earnings/route.js", "/api/kyc/documents/route": "app/api/kyc/documents/route.js", "/api/mining-units/route": "app/api/mining-units/route.js", "/api/referrals/tree/route": "app/api/referrals/tree/route.js", "/api/support/tickets/route": "app/api/support/tickets/route.js", "/api/user/notification-settings/route": "app/api/user/notification-settings/route.js", "/api/wallet/balance/route": "app/api/wallet/balance/route.js", "/api/wallet/withdraw/route": "app/api/wallet/withdraw/route.js", "/api/wallet/withdrawal-settings/route": "app/api/wallet/withdrawal-settings/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js"}