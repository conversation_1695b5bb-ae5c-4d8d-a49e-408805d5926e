'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';

interface SystemLog {
  id: string;
  action: string;
  userId?: string;
  adminId?: string;
  details?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  user?: {
    name: string;
    email: string;
  };
}

interface ErrorLogsViewerProps {
  className?: string;
}

export function ErrorLogsViewer({ className = '' }: ErrorLogsViewerProps) {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    action: '',
    userId: '',
    dateFrom: '',
    dateTo: '',
    search: '',
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const logsPerPage = 50;

  useEffect(() => {
    fetchLogs();
  }, [currentPage, filters]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: logsPerPage.toString(),
        ...filters,
      });

      const response = await fetch(`/api/admin/logs?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch logs');
      }

      const data = await response.json();
      setLogs(data.logs || []);
      setTotalPages(Math.ceil((data.total || 0) / logsPerPage));
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch logs');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setFilters({
      action: '',
      userId: '',
      dateFrom: '',
      dateTo: '',
      search: '',
    });
    setCurrentPage(1);
  };

  const exportLogs = async () => {
    try {
      const params = new URLSearchParams(filters);
      const response = await fetch(`/api/admin/logs/export?${params}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to export logs');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to export logs');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getActionColor = (action: string) => {
    if (action.includes('ERROR')) return 'text-red-600 bg-red-50';
    if (action.includes('LOGIN')) return 'text-blue-600 bg-blue-50';
    if (action.includes('ADMIN')) return 'text-purple-600 bg-purple-50';
    if (action.includes('PAYMENT') || action.includes('WALLET')) return 'text-green-600 bg-green-50';
    return 'text-gray-600 bg-gray-50';
  };

  const parseDetails = (details: string | null) => {
    if (!details) return null;
    try {
      return JSON.parse(details);
    } catch {
      return details;
    }
  };

  if (loading && logs.length === 0) {
    return (
      <div className={`bg-slate-800 rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-slate-400">Loading error logs...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-slate-800 rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">System Error Logs</h3>
        <div className="flex gap-2">
          <Button
            onClick={exportLogs}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            Export CSV
          </Button>
          <Button
            onClick={fetchLogs}
            className="bg-slate-600 hover:bg-slate-700 text-white"
          >
            Refresh
          </Button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded text-red-200">
          {error}
        </div>
      )}

      {/* Filters */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <input
          type="text"
          placeholder="Search..."
          value={filters.search}
          onChange={(e) => handleFilterChange('search', e.target.value)}
          className="px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-slate-400"
        />
        <select
          value={filters.action}
          onChange={(e) => handleFilterChange('action', e.target.value)}
          className="px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
        >
          <option value="">All Actions</option>
          <option value="ERROR">Errors Only</option>
          <option value="LOGIN">Login Events</option>
          <option value="ADMIN">Admin Actions</option>
          <option value="PAYMENT">Payment Events</option>
        </select>
        <input
          type="text"
          placeholder="User ID"
          value={filters.userId}
          onChange={(e) => handleFilterChange('userId', e.target.value)}
          className="px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white placeholder-slate-400"
        />
        <input
          type="date"
          value={filters.dateFrom}
          onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
          className="px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
        />
        <input
          type="date"
          value={filters.dateTo}
          onChange={(e) => handleFilterChange('dateTo', e.target.value)}
          className="px-3 py-2 bg-slate-700 border border-slate-600 rounded text-white"
        />
      </div>

      <div className="flex justify-between items-center mb-4">
        <Button
          onClick={clearFilters}
          className="bg-slate-600 hover:bg-slate-700 text-white text-sm"
        >
          Clear Filters
        </Button>
        <div className="text-slate-400 text-sm">
          Page {currentPage} of {totalPages}
        </div>
      </div>

      {/* Logs Table */}
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b border-slate-700">
              <th className="text-left py-3 px-2 text-slate-300">Time</th>
              <th className="text-left py-3 px-2 text-slate-300">Action</th>
              <th className="text-left py-3 px-2 text-slate-300">User</th>
              <th className="text-left py-3 px-2 text-slate-300">IP Address</th>
              <th className="text-left py-3 px-2 text-slate-300">Details</th>
            </tr>
          </thead>
          <tbody>
            {logs.map((log) => (
              <tr
                key={log.id}
                className="border-b border-slate-700 hover:bg-slate-700/50 cursor-pointer"
                onClick={() => {
                  setSelectedLog(log);
                  setShowDetails(true);
                }}
              >
                <td className="py-3 px-2 text-slate-300">
                  {formatDate(log.createdAt)}
                </td>
                <td className="py-3 px-2">
                  <span className={`px-2 py-1 rounded text-xs ${getActionColor(log.action)}`}>
                    {log.action}
                  </span>
                </td>
                <td className="py-3 px-2 text-slate-300">
                  {log.user ? (
                    <div>
                      <div className="font-medium">{log.user.name}</div>
                      <div className="text-xs text-slate-400">{log.user.email}</div>
                    </div>
                  ) : (
                    <span className="text-slate-500">N/A</span>
                  )}
                </td>
                <td className="py-3 px-2 text-slate-400">
                  {log.ipAddress || 'N/A'}
                </td>
                <td className="py-3 px-2 text-slate-400">
                  {log.details ? (
                    <span className="text-blue-400 hover:text-blue-300">
                      View Details →
                    </span>
                  ) : (
                    'No details'
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex justify-center mt-6 gap-2">
        <Button
          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
          disabled={currentPage === 1}
          className="bg-slate-600 hover:bg-slate-700 text-white disabled:opacity-50"
        >
          Previous
        </Button>
        <span className="px-4 py-2 text-slate-300">
          {currentPage} / {totalPages}
        </span>
        <Button
          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
          disabled={currentPage === totalPages}
          className="bg-slate-600 hover:bg-slate-700 text-white disabled:opacity-50"
        >
          Next
        </Button>
      </div>

      {/* Details Modal */}
      {showDetails && selectedLog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto">
            <div className="flex justify-between items-center mb-4">
              <h4 className="text-lg font-semibold text-white">Log Details</h4>
              <Button
                onClick={() => setShowDetails(false)}
                className="bg-slate-600 hover:bg-slate-700 text-white"
              >
                Close
              </Button>
            </div>
            <div className="space-y-4 text-sm">
              <div>
                <span className="text-slate-400">Action:</span>
                <span className="ml-2 text-white">{selectedLog.action}</span>
              </div>
              <div>
                <span className="text-slate-400">Time:</span>
                <span className="ml-2 text-white">{formatDate(selectedLog.createdAt)}</span>
              </div>
              {selectedLog.user && (
                <div>
                  <span className="text-slate-400">User:</span>
                  <span className="ml-2 text-white">{selectedLog.user.name} ({selectedLog.user.email})</span>
                </div>
              )}
              {selectedLog.ipAddress && (
                <div>
                  <span className="text-slate-400">IP Address:</span>
                  <span className="ml-2 text-white">{selectedLog.ipAddress}</span>
                </div>
              )}
              {selectedLog.userAgent && (
                <div>
                  <span className="text-slate-400">User Agent:</span>
                  <span className="ml-2 text-white break-all">{selectedLog.userAgent}</span>
                </div>
              )}
              {selectedLog.details && (
                <div>
                  <span className="text-slate-400">Details:</span>
                  <pre className="mt-2 p-3 bg-slate-900 rounded text-green-400 text-xs overflow-auto">
                    {JSON.stringify(parseDetails(selectedLog.details), null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
