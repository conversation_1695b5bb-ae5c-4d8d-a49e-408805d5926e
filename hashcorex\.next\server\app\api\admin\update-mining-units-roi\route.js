const CHUNK_PUBLIC_PATH = "server/app/api/admin/update-mining-units-roi/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_lib_referral_ts_49cc1019._.js");
runtime.loadChunk("server/chunks/node_modules_7535dc4e._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__54d17948._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/admin/update-mining-units-roi/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/update-mining-units-roi/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/admin/update-mining-units-roi/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
