{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/mining.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { miningUnitDb, transactionDb, systemLogDb, adminSettingsDb } from './database';\n\n// Note: User active status is now handled dynamically in the referral system\n// The isActive field in the database is only used for account access control\n// Mining activity status is computed on-demand for binary tree display\n\n// Calculate dynamic ROI based on TH/s amount and admin-configured ranges\nexport async function calculateDynamicROI(thsAmount: number): Promise<number> {\n  try {\n    // Get earnings ranges from admin settings\n    const earningsRangesStr = await adminSettingsDb.get('earningsRanges');\n    let earningsRanges;\n\n    if (earningsRangesStr) {\n      try {\n        earningsRanges = JSON.parse(earningsRangesStr);\n      } catch (parseError) {\n        console.error('Error parsing earnings ranges:', parseError);\n        earningsRanges = null;\n      }\n    }\n\n    // Fallback to default ranges if not configured\n    if (!earningsRanges || !Array.isArray(earningsRanges)) {\n      earningsRanges = [\n        { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n        { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n        { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n      ];\n    }\n\n    // Find the appropriate range for the TH/s amount\n    const applicableRange = earningsRanges.find((range: any) =>\n      thsAmount >= range.minTHS && thsAmount <= range.maxTHS\n    );\n\n    let minROI: number;\n    let maxROI: number;\n\n    if (applicableRange) {\n      minROI = applicableRange.dailyReturnMin;\n      maxROI = applicableRange.dailyReturnMax;\n    } else {\n      // Fallback to highest range if no match found\n      const highestRange = earningsRanges[earningsRanges.length - 1];\n      minROI = highestRange.dailyReturnMin;\n      maxROI = highestRange.dailyReturnMax;\n    }\n\n    // Add randomization within the range\n    const randomROI = minROI + (Math.random() * (maxROI - minROI));\n\n    // Round to 2 decimal places\n    return Math.round(randomROI * 100) / 100;\n  } catch (error) {\n    console.error('Error calculating dynamic ROI:', error);\n    // Fallback to default values based on TH/s amount\n    if (thsAmount >= 50) return 0.6;\n    if (thsAmount >= 10) return 0.5;\n    return 0.4;\n  }\n}\n\n// Validate monthly return doesn't exceed admin-configured limits for specific TH/s amount\nexport async function validateMonthlyReturn(dailyROI: number, thsAmount: number): Promise<boolean> {\n  try {\n    // Get earnings ranges from admin settings\n    const earningsRangesStr = await adminSettingsDb.get('earningsRanges');\n    let earningsRanges;\n\n    if (earningsRangesStr) {\n      try {\n        earningsRanges = JSON.parse(earningsRangesStr);\n      } catch (parseError) {\n        console.error('Error parsing earnings ranges:', parseError);\n        earningsRanges = null;\n      }\n    }\n\n    // Fallback to default ranges if not configured\n    if (!earningsRanges || !Array.isArray(earningsRanges)) {\n      earningsRanges = [\n        { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n        { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n        { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n      ];\n    }\n\n    // Find the appropriate range for the TH/s amount\n    const applicableRange = earningsRanges.find((range: any) =>\n      thsAmount >= range.minTHS && thsAmount <= range.maxTHS\n    );\n\n    let monthlyMin = 10.0;\n    let monthlyMax = 15.0;\n\n    if (applicableRange) {\n      monthlyMin = applicableRange.monthlyReturnMin || 10.0;\n      monthlyMax = applicableRange.monthlyReturnMax || 15.0;\n    } else {\n      // Fallback to highest range if no match found\n      const highestRange = earningsRanges[earningsRanges.length - 1];\n      monthlyMin = highestRange.monthlyReturnMin || 10.0;\n      monthlyMax = highestRange.monthlyReturnMax || 15.0;\n    }\n\n    const monthlyReturn = dailyROI * 30; // Approximate monthly return\n    return monthlyReturn >= monthlyMin && monthlyReturn <= monthlyMax;\n  } catch (error) {\n    console.error('Error validating monthly return:', error);\n    // Fallback to default 10-15% range\n    const monthlyReturn = dailyROI * 30;\n    return monthlyReturn >= 10 && monthlyReturn <= 15;\n  }\n}\n\n// Get monthly return limits for a specific TH/s amount\nexport async function getMonthlyReturnLimits(thsAmount: number): Promise<{ min: number; max: number }> {\n  try {\n    // Get earnings ranges from admin settings\n    const earningsRangesStr = await adminSettingsDb.get('earningsRanges');\n    let earningsRanges;\n\n    if (earningsRangesStr) {\n      try {\n        earningsRanges = JSON.parse(earningsRangesStr);\n      } catch (parseError) {\n        console.error('Error parsing earnings ranges:', parseError);\n        earningsRanges = null;\n      }\n    }\n\n    // Fallback to default ranges if not configured\n    if (!earningsRanges || !Array.isArray(earningsRanges)) {\n      earningsRanges = [\n        { minTHS: 0, maxTHS: 10, dailyReturnMin: 0.3, dailyReturnMax: 0.5, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n        { minTHS: 10, maxTHS: 50, dailyReturnMin: 0.4, dailyReturnMax: 0.6, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n        { minTHS: 50, maxTHS: 999999, dailyReturnMin: 0.5, dailyReturnMax: 0.7, monthlyReturnMin: 10.0, monthlyReturnMax: 15.0 },\n      ];\n    }\n\n    // Find the appropriate range for the TH/s amount\n    const applicableRange = earningsRanges.find((range: any) =>\n      thsAmount >= range.minTHS && thsAmount <= range.maxTHS\n    );\n\n    if (applicableRange) {\n      return {\n        min: applicableRange.monthlyReturnMin || 10.0,\n        max: applicableRange.monthlyReturnMax || 15.0,\n      };\n    } else {\n      // Fallback to highest range if no match found\n      const highestRange = earningsRanges[earningsRanges.length - 1];\n      return {\n        min: highestRange.monthlyReturnMin || 10.0,\n        max: highestRange.monthlyReturnMax || 15.0,\n      };\n    }\n  } catch (error) {\n    console.error('Error getting monthly return limits:', error);\n    // Fallback to default 10-15% range\n    return { min: 10.0, max: 15.0 };\n  }\n}\n\n// Update existing mining units when earnings configuration changes\nexport async function updateExistingMiningUnitsROI() {\n  try {\n    console.log('Updating existing mining units with new ROI configuration...');\n\n    // Get all active mining units\n    const activeMiningUnits = await prisma.miningUnit.findMany({\n      where: {\n        status: 'ACTIVE',\n        expiryDate: {\n          gt: new Date(),\n        },\n      },\n    });\n\n    console.log(`Found ${activeMiningUnits.length} active mining units to update`);\n\n    const updateResults = [];\n\n    for (const unit of activeMiningUnits) {\n      try {\n        // Calculate new ROI based on current TH/s amount\n        const newROI = await calculateDynamicROI(unit.thsAmount);\n\n        // Update the mining unit with new ROI\n        await prisma.miningUnit.update({\n          where: { id: unit.id },\n          data: { dailyROI: newROI },\n        });\n\n        updateResults.push({\n          unitId: unit.id,\n          userId: unit.userId,\n          thsAmount: unit.thsAmount,\n          oldROI: unit.dailyROI,\n          newROI,\n        });\n\n        console.log(`Updated unit ${unit.id}: ${unit.dailyROI}% -> ${newROI}%`);\n      } catch (unitError) {\n        console.error(`Error updating unit ${unit.id}:`, unitError);\n      }\n    }\n\n    // Log the update process\n    await systemLogDb.create({\n      action: 'MINING_UNITS_ROI_UPDATED',\n      details: {\n        unitsUpdated: updateResults.length,\n        totalUnits: activeMiningUnits.length,\n        updateResults,\n        timestamp: new Date().toISOString(),\n      },\n    });\n\n    console.log(`Successfully updated ${updateResults.length} mining units with new ROI configuration`);\n\n    return {\n      success: true,\n      unitsUpdated: updateResults.length,\n      totalUnits: activeMiningUnits.length,\n      updateResults,\n    };\n  } catch (error) {\n    console.error('Error updating existing mining units ROI:', error);\n    throw error;\n  }\n}\n\n\n\n// Calculate daily ROI for all active mining units\nexport async function calculateDailyROI() {\n  try {\n    console.log('Starting daily ROI calculation...');\n\n    // Get all active mining units\n    const activeMiningUnits = await prisma.miningUnit.findMany({\n      where: {\n        status: 'ACTIVE',\n        expiryDate: {\n          gt: new Date(),\n        },\n      },\n      include: {\n        user: true,\n      },\n    });\n\n    console.log(`Found ${activeMiningUnits.length} active mining units`);\n\n    const results = [];\n\n    for (const unit of activeMiningUnits) {\n      try {\n        // Calculate daily earnings\n        const dailyEarnings = (unit.investmentAmount * unit.dailyROI) / 100;\n\n        // Check if unit should expire (5x investment earned)\n        const totalEarningsAfter = unit.totalEarned + dailyEarnings;\n        const maxEarnings = unit.investmentAmount * 5;\n\n        let finalEarnings = dailyEarnings;\n        let shouldExpire = false;\n\n        if (totalEarningsAfter >= maxEarnings) {\n          // Cap earnings at 5x investment\n          finalEarnings = maxEarnings - unit.totalEarned;\n          shouldExpire = true;\n        }\n\n        if (finalEarnings > 0) {\n          // Update mining unit total earned\n          await miningUnitDb.updateTotalEarned(unit.id, finalEarnings);\n\n          // Create pending earnings transaction\n          await transactionDb.create({\n            userId: unit.userId,\n            type: 'MINING_EARNINGS',\n            amount: finalEarnings,\n            description: `Daily mining earnings - ${unit.thsAmount} TH/s`,\n            status: 'PENDING',\n          });\n\n          results.push({\n            unitId: unit.id,\n            userId: unit.userId,\n            earnings: finalEarnings,\n            expired: shouldExpire,\n          });\n        }\n\n        // Expire unit if necessary\n        if (shouldExpire) {\n          await miningUnitDb.expireUnit(unit.id);\n\n          // Note: User active status is now computed dynamically\n\n          await systemLogDb.create({\n            action: 'MINING_UNIT_EXPIRED',\n            userId: unit.userId,\n            details: {\n              miningUnitId: unit.id,\n              reason: '5x_investment_reached',\n              totalEarned: totalEarningsAfter,\n              investmentAmount: unit.investmentAmount,\n            },\n          });\n        }\n\n      } catch (unitError) {\n        console.error(`Error processing unit ${unit.id}:`, unitError);\n      }\n    }\n\n    // Log the daily ROI calculation\n    await systemLogDb.create({\n      action: 'DAILY_ROI_CALCULATED',\n      details: {\n        unitsProcessed: activeMiningUnits.length,\n        totalEarnings: results.reduce((sum, r) => sum + r.earnings, 0),\n        expiredUnits: results.filter(r => r.expired).length,\n        timestamp: new Date().toISOString(),\n      },\n    });\n\n    console.log(`Daily ROI calculation completed. Processed ${results.length} units.`);\n    return results;\n\n  } catch (error) {\n    console.error('Daily ROI calculation error:', error);\n    throw error;\n  }\n}\n\n// Process weekly earnings distribution (Saturday 15:00 UTC)\nexport async function processWeeklyEarnings() {\n  try {\n    console.log('Starting weekly earnings distribution...');\n\n    // Get all pending mining earnings\n    const pendingEarnings = await prisma.transaction.findMany({\n      where: {\n        type: 'MINING_EARNINGS',\n        status: 'PENDING',\n      },\n      include: {\n        user: true,\n      },\n    });\n\n    console.log(`Found ${pendingEarnings.length} pending earnings transactions`);\n\n    const userEarnings = new Map<string, number>();\n\n    // Group earnings by user\n    for (const transaction of pendingEarnings) {\n      const currentTotal = userEarnings.get(transaction.userId) || 0;\n      userEarnings.set(transaction.userId, currentTotal + transaction.amount);\n    }\n\n    const results = [];\n\n    // Process each user's earnings\n    for (const [userId, totalEarnings] of userEarnings) {\n      try {\n        // Mark all pending transactions as completed\n        await prisma.transaction.updateMany({\n          where: {\n            userId,\n            type: 'MINING_EARNINGS',\n            status: 'PENDING',\n          },\n          data: {\n            status: 'COMPLETED',\n          },\n        });\n\n        results.push({\n          userId,\n          totalEarnings,\n        });\n\n      } catch (userError) {\n        console.error(`Error processing earnings for user ${userId}:`, userError);\n      }\n    }\n\n    // Log the weekly distribution\n    await systemLogDb.create({\n      action: 'WEEKLY_EARNINGS_DISTRIBUTED',\n      details: {\n        usersProcessed: results.length,\n        totalDistributed: results.reduce((sum, r) => sum + r.totalEarnings, 0),\n        transactionsProcessed: pendingEarnings.length,\n        timestamp: new Date().toISOString(),\n      },\n    });\n\n    console.log(`Weekly earnings distribution completed. Processed ${results.length} users.`);\n    return results;\n\n  } catch (error) {\n    console.error('Weekly earnings distribution error:', error);\n    throw error;\n  }\n}\n\n// Check and expire mining units that have reached 12 months\nexport async function expireOldMiningUnits() {\n  try {\n    console.log('Checking for expired mining units...');\n\n    const expiredUnits = await prisma.miningUnit.findMany({\n      where: {\n        status: 'ACTIVE',\n        expiryDate: {\n          lte: new Date(),\n        },\n      },\n    });\n\n    console.log(`Found ${expiredUnits.length} units to expire`);\n\n    for (const unit of expiredUnits) {\n      await miningUnitDb.expireUnit(unit.id);\n\n      // Note: User active status is now computed dynamically\n\n      await systemLogDb.create({\n        action: 'MINING_UNIT_EXPIRED',\n        userId: unit.userId,\n        details: {\n          miningUnitId: unit.id,\n          reason: '12_months_reached',\n          totalEarned: unit.totalEarned,\n          investmentAmount: unit.investmentAmount,\n        },\n      });\n    }\n\n    return expiredUnits.length;\n\n  } catch (error) {\n    console.error('Mining unit expiry check error:', error);\n    throw error;\n  }\n}\n\n// Get mining statistics\nexport async function getMiningStats() {\n  try {\n    const stats = await prisma.$transaction([\n      // Total TH/s sold\n      prisma.miningUnit.aggregate({\n        _sum: {\n          thsAmount: true,\n        },\n      }),\n      \n      // Active TH/s\n      prisma.miningUnit.aggregate({\n        where: {\n          status: 'ACTIVE',\n        },\n        _sum: {\n          thsAmount: true,\n        },\n      }),\n      \n      // Total investment\n      prisma.miningUnit.aggregate({\n        _sum: {\n          investmentAmount: true,\n        },\n      }),\n      \n      // Total earnings distributed\n      prisma.transaction.aggregate({\n        where: {\n          type: 'MINING_EARNINGS',\n          status: 'COMPLETED',\n        },\n        _sum: {\n          amount: true,\n        },\n      }),\n      \n      // Active mining units count\n      prisma.miningUnit.count({\n        where: {\n          status: 'ACTIVE',\n        },\n      }),\n      \n      // Total mining units count\n      prisma.miningUnit.count(),\n    ]);\n\n    return {\n      totalTHSSold: stats[0]._sum.thsAmount || 0,\n      activeTHS: stats[1]._sum.thsAmount || 0,\n      totalInvestment: stats[2]._sum.investmentAmount || 0,\n      totalEarningsDistributed: stats[3]._sum.amount || 0,\n      activeMiningUnits: stats[4],\n      totalMiningUnits: stats[5],\n    };\n\n  } catch (error) {\n    console.error('Mining stats error:', error);\n    throw error;\n  }\n}\n\n// Calculate user's estimated earnings\nexport async function calculateEstimatedEarnings(userId: string) {\n  try {\n    const activeMiningUnits = await miningUnitDb.findActiveByUserId(userId);\n    \n    if (activeMiningUnits.length === 0) {\n      return {\n        next7Days: 0,\n        next30Days: 0,\n        next365Days: 0,\n      };\n    }\n\n    let totalDaily = 0;\n    \n    for (const unit of activeMiningUnits) {\n      const dailyEarnings = (unit.investmentAmount * unit.dailyROI) / 100;\n      const maxEarnings = unit.investmentAmount * 5;\n      const remainingEarnings = maxEarnings - unit.totalEarned;\n      \n      // Use the lower of daily earnings or remaining earnings\n      totalDaily += Math.min(dailyEarnings, remainingEarnings);\n    }\n\n    return {\n      next7Days: totalDaily * 7,\n      next30Days: totalDaily * 30,\n      next365Days: totalDaily * 365,\n    };\n\n  } catch (error) {\n    console.error('Estimated earnings calculation error:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAOO,eAAe,oBAAoB,SAAiB;IACzD,IAAI;QACF,0CAA0C;QAC1C,MAAM,oBAAoB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QACpD,IAAI;QAEJ,IAAI,mBAAmB;YACrB,IAAI;gBACF,iBAAiB,KAAK,KAAK,CAAC;YAC9B,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,iBAAiB;YACnB;QACF;QAEA,+CAA+C;QAC/C,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO,CAAC,iBAAiB;YACrD,iBAAiB;gBACf;oBAAE,QAAQ;oBAAG,QAAQ;oBAAI,gBAAgB;oBAAK,gBAAgB;oBAAK,kBAAkB;oBAAM,kBAAkB;gBAAK;gBAClH;oBAAE,QAAQ;oBAAI,QAAQ;oBAAI,gBAAgB;oBAAK,gBAAgB;oBAAK,kBAAkB;oBAAM,kBAAkB;gBAAK;gBACnH;oBAAE,QAAQ;oBAAI,QAAQ;oBAAQ,gBAAgB;oBAAK,gBAAgB;oBAAK,kBAAkB;oBAAM,kBAAkB;gBAAK;aACxH;QACH;QAEA,iDAAiD;QACjD,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAC,QAC3C,aAAa,MAAM,MAAM,IAAI,aAAa,MAAM,MAAM;QAGxD,IAAI;QACJ,IAAI;QAEJ,IAAI,iBAAiB;YACnB,SAAS,gBAAgB,cAAc;YACvC,SAAS,gBAAgB,cAAc;QACzC,OAAO;YACL,8CAA8C;YAC9C,MAAM,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;YAC9D,SAAS,aAAa,cAAc;YACpC,SAAS,aAAa,cAAc;QACtC;QAEA,qCAAqC;QACrC,MAAM,YAAY,SAAU,KAAK,MAAM,KAAK,CAAC,SAAS,MAAM;QAE5D,4BAA4B;QAC5B,OAAO,KAAK,KAAK,CAAC,YAAY,OAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,kDAAkD;QAClD,IAAI,aAAa,IAAI,OAAO;QAC5B,IAAI,aAAa,IAAI,OAAO;QAC5B,OAAO;IACT;AACF;AAGO,eAAe,sBAAsB,QAAgB,EAAE,SAAiB;IAC7E,IAAI;QACF,0CAA0C;QAC1C,MAAM,oBAAoB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QACpD,IAAI;QAEJ,IAAI,mBAAmB;YACrB,IAAI;gBACF,iBAAiB,KAAK,KAAK,CAAC;YAC9B,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,iBAAiB;YACnB;QACF;QAEA,+CAA+C;QAC/C,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO,CAAC,iBAAiB;YACrD,iBAAiB;gBACf;oBAAE,QAAQ;oBAAG,QAAQ;oBAAI,gBAAgB;oBAAK,gBAAgB;oBAAK,kBAAkB;oBAAM,kBAAkB;gBAAK;gBAClH;oBAAE,QAAQ;oBAAI,QAAQ;oBAAI,gBAAgB;oBAAK,gBAAgB;oBAAK,kBAAkB;oBAAM,kBAAkB;gBAAK;gBACnH;oBAAE,QAAQ;oBAAI,QAAQ;oBAAQ,gBAAgB;oBAAK,gBAAgB;oBAAK,kBAAkB;oBAAM,kBAAkB;gBAAK;aACxH;QACH;QAEA,iDAAiD;QACjD,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAC,QAC3C,aAAa,MAAM,MAAM,IAAI,aAAa,MAAM,MAAM;QAGxD,IAAI,aAAa;QACjB,IAAI,aAAa;QAEjB,IAAI,iBAAiB;YACnB,aAAa,gBAAgB,gBAAgB,IAAI;YACjD,aAAa,gBAAgB,gBAAgB,IAAI;QACnD,OAAO;YACL,8CAA8C;YAC9C,MAAM,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;YAC9D,aAAa,aAAa,gBAAgB,IAAI;YAC9C,aAAa,aAAa,gBAAgB,IAAI;QAChD;QAEA,MAAM,gBAAgB,WAAW,IAAI,6BAA6B;QAClE,OAAO,iBAAiB,cAAc,iBAAiB;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,mCAAmC;QACnC,MAAM,gBAAgB,WAAW;QACjC,OAAO,iBAAiB,MAAM,iBAAiB;IACjD;AACF;AAGO,eAAe,uBAAuB,SAAiB;IAC5D,IAAI;QACF,0CAA0C;QAC1C,MAAM,oBAAoB,MAAM,wHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC;QACpD,IAAI;QAEJ,IAAI,mBAAmB;YACrB,IAAI;gBACF,iBAAiB,KAAK,KAAK,CAAC;YAC9B,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,iBAAiB;YACnB;QACF;QAEA,+CAA+C;QAC/C,IAAI,CAAC,kBAAkB,CAAC,MAAM,OAAO,CAAC,iBAAiB;YACrD,iBAAiB;gBACf;oBAAE,QAAQ;oBAAG,QAAQ;oBAAI,gBAAgB;oBAAK,gBAAgB;oBAAK,kBAAkB;oBAAM,kBAAkB;gBAAK;gBAClH;oBAAE,QAAQ;oBAAI,QAAQ;oBAAI,gBAAgB;oBAAK,gBAAgB;oBAAK,kBAAkB;oBAAM,kBAAkB;gBAAK;gBACnH;oBAAE,QAAQ;oBAAI,QAAQ;oBAAQ,gBAAgB;oBAAK,gBAAgB;oBAAK,kBAAkB;oBAAM,kBAAkB;gBAAK;aACxH;QACH;QAEA,iDAAiD;QACjD,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAC,QAC3C,aAAa,MAAM,MAAM,IAAI,aAAa,MAAM,MAAM;QAGxD,IAAI,iBAAiB;YACnB,OAAO;gBACL,KAAK,gBAAgB,gBAAgB,IAAI;gBACzC,KAAK,gBAAgB,gBAAgB,IAAI;YAC3C;QACF,OAAO;YACL,8CAA8C;YAC9C,MAAM,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE;YAC9D,OAAO;gBACL,KAAK,aAAa,gBAAgB,IAAI;gBACtC,KAAK,aAAa,gBAAgB,IAAI;YACxC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,mCAAmC;QACnC,OAAO;YAAE,KAAK;YAAM,KAAK;QAAK;IAChC;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,8BAA8B;QAC9B,MAAM,oBAAoB,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACzD,OAAO;gBACL,QAAQ;gBACR,YAAY;oBACV,IAAI,IAAI;gBACV;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,kBAAkB,MAAM,CAAC,8BAA8B,CAAC;QAE7E,MAAM,gBAAgB,EAAE;QAExB,KAAK,MAAM,QAAQ,kBAAmB;YACpC,IAAI;gBACF,iDAAiD;gBACjD,MAAM,SAAS,MAAM,oBAAoB,KAAK,SAAS;gBAEvD,sCAAsC;gBACtC,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;oBAC7B,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBAAE,UAAU;oBAAO;gBAC3B;gBAEA,cAAc,IAAI,CAAC;oBACjB,QAAQ,KAAK,EAAE;oBACf,QAAQ,KAAK,MAAM;oBACnB,WAAW,KAAK,SAAS;oBACzB,QAAQ,KAAK,QAAQ;oBACrB;gBACF;gBAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACxE,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;YACnD;QACF;QAEA,yBAAyB;QACzB,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACvB,QAAQ;YACR,SAAS;gBACP,cAAc,cAAc,MAAM;gBAClC,YAAY,kBAAkB,MAAM;gBACpC;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,cAAc,MAAM,CAAC,wCAAwC,CAAC;QAElG,OAAO;YACL,SAAS;YACT,cAAc,cAAc,MAAM;YAClC,YAAY,kBAAkB,MAAM;YACpC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,MAAM;IACR;AACF;AAKO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,8BAA8B;QAC9B,MAAM,oBAAoB,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACzD,OAAO;gBACL,QAAQ;gBACR,YAAY;oBACV,IAAI,IAAI;gBACV;YACF;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,kBAAkB,MAAM,CAAC,oBAAoB,CAAC;QAEnE,MAAM,UAAU,EAAE;QAElB,KAAK,MAAM,QAAQ,kBAAmB;YACpC,IAAI;gBACF,2BAA2B;gBAC3B,MAAM,gBAAgB,AAAC,KAAK,gBAAgB,GAAG,KAAK,QAAQ,GAAI;gBAEhE,qDAAqD;gBACrD,MAAM,qBAAqB,KAAK,WAAW,GAAG;gBAC9C,MAAM,cAAc,KAAK,gBAAgB,GAAG;gBAE5C,IAAI,gBAAgB;gBACpB,IAAI,eAAe;gBAEnB,IAAI,sBAAsB,aAAa;oBACrC,gCAAgC;oBAChC,gBAAgB,cAAc,KAAK,WAAW;oBAC9C,eAAe;gBACjB;gBAEA,IAAI,gBAAgB,GAAG;oBACrB,kCAAkC;oBAClC,MAAM,wHAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE;oBAE9C,sCAAsC;oBACtC,MAAM,wHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;wBACzB,QAAQ,KAAK,MAAM;wBACnB,MAAM;wBACN,QAAQ;wBACR,aAAa,CAAC,wBAAwB,EAAE,KAAK,SAAS,CAAC,KAAK,CAAC;wBAC7D,QAAQ;oBACV;oBAEA,QAAQ,IAAI,CAAC;wBACX,QAAQ,KAAK,EAAE;wBACf,QAAQ,KAAK,MAAM;wBACnB,UAAU;wBACV,SAAS;oBACX;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,cAAc;oBAChB,MAAM,wHAAA,CAAA,eAAY,CAAC,UAAU,CAAC,KAAK,EAAE;oBAErC,uDAAuD;oBAEvD,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;wBACvB,QAAQ;wBACR,QAAQ,KAAK,MAAM;wBACnB,SAAS;4BACP,cAAc,KAAK,EAAE;4BACrB,QAAQ;4BACR,aAAa;4BACb,kBAAkB,KAAK,gBAAgB;wBACzC;oBACF;gBACF;YAEF,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;YACrD;QACF;QAEA,gCAAgC;QAChC,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACvB,QAAQ;YACR,SAAS;gBACP,gBAAgB,kBAAkB,MAAM;gBACxC,eAAe,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE;gBAC5D,cAAc,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;gBACnD,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;QACjF,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,kCAAkC;QAClC,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACxD,OAAO;gBACL,MAAM;gBACN,QAAQ;YACV;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,8BAA8B,CAAC;QAE3E,MAAM,eAAe,IAAI;QAEzB,yBAAyB;QACzB,KAAK,MAAM,eAAe,gBAAiB;YACzC,MAAM,eAAe,aAAa,GAAG,CAAC,YAAY,MAAM,KAAK;YAC7D,aAAa,GAAG,CAAC,YAAY,MAAM,EAAE,eAAe,YAAY,MAAM;QACxE;QAEA,MAAM,UAAU,EAAE;QAElB,+BAA+B;QAC/B,KAAK,MAAM,CAAC,QAAQ,cAAc,IAAI,aAAc;YAClD,IAAI;gBACF,6CAA6C;gBAC7C,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,UAAU,CAAC;oBAClC,OAAO;wBACL;wBACA,MAAM;wBACN,QAAQ;oBACV;oBACA,MAAM;wBACJ,QAAQ;oBACV;gBACF;gBAEA,QAAQ,IAAI,CAAC;oBACX;oBACA;gBACF;YAEF,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YACjE;QACF;QAEA,8BAA8B;QAC9B,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACvB,QAAQ;YACR,SAAS;gBACP,gBAAgB,QAAQ,MAAM;gBAC9B,kBAAkB,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE;gBACpE,uBAAuB,gBAAgB,MAAM;gBAC7C,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,kDAAkD,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;QACxF,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACpD,OAAO;gBACL,QAAQ;gBACR,YAAY;oBACV,KAAK,IAAI;gBACX;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa,MAAM,CAAC,gBAAgB,CAAC;QAE1D,KAAK,MAAM,QAAQ,aAAc;YAC/B,MAAM,wHAAA,CAAA,eAAY,CAAC,UAAU,CAAC,KAAK,EAAE;YAErC,uDAAuD;YAEvD,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACvB,QAAQ;gBACR,QAAQ,KAAK,MAAM;gBACnB,SAAS;oBACP,cAAc,KAAK,EAAE;oBACrB,QAAQ;oBACR,aAAa,KAAK,WAAW;oBAC7B,kBAAkB,KAAK,gBAAgB;gBACzC;YACF;QACF;QAEA,OAAO,aAAa,MAAM;IAE5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC;YACtC,kBAAkB;YAClB,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC1B,MAAM;oBACJ,WAAW;gBACb;YACF;YAEA,cAAc;YACd,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC1B,OAAO;oBACL,QAAQ;gBACV;gBACA,MAAM;oBACJ,WAAW;gBACb;YACF;YAEA,mBAAmB;YACnB,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC1B,MAAM;oBACJ,kBAAkB;gBACpB;YACF;YAEA,6BAA6B;YAC7B,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;gBAC3B,OAAO;oBACL,MAAM;oBACN,QAAQ;gBACV;gBACA,MAAM;oBACJ,QAAQ;gBACV;YACF;YAEA,4BAA4B;YAC5B,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,KAAK,CAAC;gBACtB,OAAO;oBACL,QAAQ;gBACV;YACF;YAEA,2BAA2B;YAC3B,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,KAAK;SACxB;QAED,OAAO;YACL,cAAc,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI;YACzC,WAAW,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI;YACtC,iBAAiB,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,IAAI;YACnD,0BAA0B,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI;YAClD,mBAAmB,KAAK,CAAC,EAAE;YAC3B,kBAAkB,KAAK,CAAC,EAAE;QAC5B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAGO,eAAe,2BAA2B,MAAc;IAC7D,IAAI;QACF,MAAM,oBAAoB,MAAM,wHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC;QAEhE,IAAI,kBAAkB,MAAM,KAAK,GAAG;YAClC,OAAO;gBACL,WAAW;gBACX,YAAY;gBACZ,aAAa;YACf;QACF;QAEA,IAAI,aAAa;QAEjB,KAAK,MAAM,QAAQ,kBAAmB;YACpC,MAAM,gBAAgB,AAAC,KAAK,gBAAgB,GAAG,KAAK,QAAQ,GAAI;YAChE,MAAM,cAAc,KAAK,gBAAgB,GAAG;YAC5C,MAAM,oBAAoB,cAAc,KAAK,WAAW;YAExD,wDAAwD;YACxD,cAAc,KAAK,GAAG,CAAC,eAAe;QACxC;QAEA,OAAO;YACL,WAAW,aAAa;YACxB,YAAY,aAAa;YACzB,aAAa,aAAa;QAC5B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF", "debugId": null}}]}