'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Button, Input } from '@/components/ui';
import {
  Settings,
  Save,
  DollarSign,
  Percent,
  Zap,
  Users,
  Shield,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Wallet,
  ArrowUpDown,
  Globe
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface EarningsRange {
  minTHS: number;
  maxTHS: number;
  dailyReturnMin: number;
  dailyReturnMax: number;
  monthlyReturnMin: number;
  monthlyReturnMax: number;
}

interface SystemSettings {
  // Mining Unit Pricing
  thsPriceUSD: number;
  minPurchaseAmount: number;
  maxPurchaseAmount: number;

  // Dynamic Earnings Configuration
  earningsRanges: EarningsRange[];
  binaryBonusPercentage: number;
  referralBonusPercentage: number;

  // Binary Matching Settings
  maxBinaryPointsPerSide: number;
  binaryPointValue: number;
  binaryMatchingEnabled: boolean;
  binaryMatchingSchedule: string;

  // Deposit Settings
  usdtDepositAddress: string;
  minDepositAmount: number;
  maxDepositAmount: number;
  depositEnabled: boolean;
  minConfirmations: number;
  depositFeePercentage: number;

  // Withdrawal Settings
  minWithdrawalAmount: number;
  withdrawalFeeFixed: number;
  withdrawalFeePercentage: number;
  withdrawalProcessingDays: number;

  // Platform Settings
  platformFeePercentage: number;
  maintenanceMode: boolean;
  registrationEnabled: boolean;
  kycRequired: boolean;
}

type TabType = 'pricing' | 'earnings' | 'binary' | 'deposits' | 'withdrawals' | 'platform';

export const SystemSettings: React.FC = () => {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('pricing');
  const [updatingROI, setUpdatingROI] = useState(false);
  const [processingMatching, setProcessingMatching] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/settings', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSettings(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!settings) return;

    try {
      setSaving(true);
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        setSaveSuccess(true);
        setTimeout(() => setSaveSuccess(false), 3000);
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (key: keyof SystemSettings, value: any) => {
    if (!settings) return;
    setSettings({ ...settings, [key]: value });
  };

  const updateEarningsRange = (index: number, field: keyof EarningsRange, value: number) => {
    if (!settings) return;
    const newRanges = [...settings.earningsRanges];
    newRanges[index] = { ...newRanges[index], [field]: value };
    setSettings({ ...settings, earningsRanges: newRanges });
  };

  const addEarningsRange = () => {
    if (!settings) return;
    const newRange: EarningsRange = {
      minTHS: 0,
      maxTHS: 100,
      dailyReturnMin: 0.5,
      dailyReturnMax: 1.0,
      monthlyReturnMin: 10.0,
      monthlyReturnMax: 15.0,
    };
    setSettings({ ...settings, earningsRanges: [...settings.earningsRanges, newRange] });
  };

  const removeEarningsRange = (index: number) => {
    if (!settings) return;
    const newRanges = settings.earningsRanges.filter((_, i) => i !== index);
    setSettings({ ...settings, earningsRanges: newRanges });
  };

  const handleUpdateMiningUnitsROI = async () => {
    if (!confirm('Are you sure you want to update all existing mining units with the new ROI configuration? This will recalculate ROI for all active mining units based on their TH/s amounts.')) {
      return;
    }

    setUpdatingROI(true);
    try {
      const response = await fetch('/api/admin/update-mining-units-roi', {
        method: 'POST',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        alert(`Mining units ROI update completed successfully!\n\nUnits updated: ${data.data.unitsUpdated}\nTotal units: ${data.data.totalUnits}`);
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Mining units ROI update error:', error);
      alert('Failed to update mining units ROI. Please try again.');
    } finally {
      setUpdatingROI(false);
    }
  };

  const handleManualBinaryMatching = async () => {
    if (!confirm('Are you sure you want to manually trigger binary matching? This will process all users with binary points and distribute earnings.')) {
      return;
    }

    setProcessingMatching(true);
    try {
      const response = await fetch('/api/admin/binary-matching/manual', {
        method: 'POST',
        credentials: 'include',
      });

      const data = await response.json();

      if (data.success) {
        alert(`Binary matching completed successfully!\n\nUsers processed: ${data.data.usersProcessed}\nTotal payouts: $${data.data.totalPayouts.toFixed(2)}`);
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Manual binary matching error:', error);
      alert('Failed to process binary matching. Please try again.');
    } finally {
      setProcessingMatching(false);
    }
  };

  const tabs = [
    { id: 'pricing' as TabType, label: 'Mining & Pricing', icon: Zap },
    { id: 'earnings' as TabType, label: 'Earnings Config', icon: TrendingUp },
    { id: 'binary' as TabType, label: 'Binary Matching', icon: ArrowUpDown },
    { id: 'deposits' as TabType, label: 'Deposits', icon: Wallet },
    { id: 'withdrawals' as TabType, label: 'Withdrawals', icon: DollarSign },
    { id: 'platform' as TabType, label: 'Platform', icon: Globe },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-700 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-slate-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">Failed to Load Settings</h3>
        <p className="text-slate-400">Unable to load system settings. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">System Settings</h1>
          <p className="text-slate-400 mt-1">Configure platform parameters and business rules</p>
        </div>
        <Button
          onClick={handleSave}
          loading={saving}
          className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white"
        >
          {saveSuccess ? (
            <>
              <CheckCircle className="h-4 w-4" />
              Saved
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Save Changes
            </>
          )}
        </Button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-slate-700">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-400'
                    : 'border-transparent text-slate-400 hover:text-slate-300 hover:border-slate-300'
                }`}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {renderTabContent()}

    </div>
  );

  function renderTabContent() {
    if (!settings) return null;

    switch (activeTab) {
      case 'pricing':
        return renderPricingTab();
      case 'earnings':
        return renderEarningsTab();
      case 'binary':
        return renderBinaryTab();
      case 'deposits':
        return renderDepositsTab();
      case 'withdrawals':
        return renderWithdrawalsTab();
      case 'platform':
        return renderPlatformTab();
      default:
        return null;
    }
  }

  function renderPricingTab() {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Zap className="h-5 w-5 text-blue-400" />
            Mining Unit Pricing
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                THS Price (USD)
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.thsPriceUSD}
                  onChange={(e) => updateSetting('thsPriceUSD', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Minimum Purchase
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.minPurchaseAmount}
                  onChange={(e) => updateSetting('minPurchaseAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Maximum Purchase
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.maxPurchaseAmount}
                  onChange={(e) => updateSetting('maxPurchaseAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  function renderEarningsTab() {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <TrendingUp className="h-5 w-5 text-orange-400" />
            Dynamic Earnings Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* TH/s Based Earnings Ranges */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-white">TH/s Based Daily Returns</h3>
              <Button
                onClick={addEarningsRange}
                className="bg-orange-600 hover:bg-orange-700 text-white text-sm"
              >
                Add Range
              </Button>
            </div>

            <div className="space-y-4">
              {settings.earningsRanges?.map((range, index) => (
                <div key={index} className="p-4 bg-slate-700 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Min TH/s
                      </label>
                      <Input
                        type="number"
                        step="0.1"
                        value={range.minTHS}
                        onChange={(e) => updateEarningsRange(index, 'minTHS', parseFloat(e.target.value) || 0)}
                        className="bg-slate-600 border-slate-500 text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Max TH/s
                      </label>
                      <Input
                        type="number"
                        step="0.1"
                        value={range.maxTHS}
                        onChange={(e) => updateEarningsRange(index, 'maxTHS', parseFloat(e.target.value) || 0)}
                        className="bg-slate-600 border-slate-500 text-white"
                      />
                    </div>
                    <div className="flex items-end">
                      <Button
                        onClick={() => removeEarningsRange(index)}
                        className="bg-red-600 hover:bg-red-700 text-white w-full"
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Min Daily Return (%)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={range.dailyReturnMin}
                        onChange={(e) => updateEarningsRange(index, 'dailyReturnMin', parseFloat(e.target.value) || 0)}
                        className="bg-slate-600 border-slate-500 text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Max Daily Return (%)
                      </label>
                      <Input
                        type="number"
                        step="0.01"
                        value={range.dailyReturnMax}
                        onChange={(e) => updateEarningsRange(index, 'dailyReturnMax', parseFloat(e.target.value) || 0)}
                        className="bg-slate-600 border-slate-500 text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Min Monthly Return (%)
                      </label>
                      <Input
                        type="number"
                        step="0.1"
                        value={range.monthlyReturnMin}
                        onChange={(e) => updateEarningsRange(index, 'monthlyReturnMin', parseFloat(e.target.value) || 0)}
                        className="bg-slate-600 border-slate-500 text-white"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-300 mb-2">
                        Max Monthly Return (%)
                      </label>
                      <Input
                        type="number"
                        step="0.1"
                        value={range.monthlyReturnMax}
                        onChange={(e) => updateEarningsRange(index, 'monthlyReturnMax', parseFloat(e.target.value) || 0)}
                        className="bg-slate-600 border-slate-500 text-white"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>



          {/* Bonus Percentages */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Binary Bonus (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.binaryBonusPercentage}
                  onChange={(e) => updateSetting('binaryBonusPercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Referral Bonus (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.referralBonusPercentage}
                  onChange={(e) => updateSetting('referralBonusPercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-orange-500"
                />
              </div>
            </div>
          </div>

          <div className="mt-4 p-4 bg-slate-700 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-sm font-medium text-slate-300 mb-2">Update Existing Mining Units</h4>
                <p className="text-xs text-slate-400">Apply new ROI configuration to all active mining units</p>
              </div>
              <Button
                onClick={handleUpdateMiningUnitsROI}
                loading={updatingROI}
                className="bg-orange-600 hover:bg-orange-700 text-white"
              >
                Update All Units
              </Button>
            </div>

            <h4 className="text-sm font-medium text-slate-300 mb-2">Important Notes:</h4>
            <ul className="text-xs text-slate-400 space-y-1">
              <li>• Changes to earnings configuration will affect new mining units automatically</li>
              <li>• Use "Update All Units" button to apply changes to existing active mining units</li>
              <li>• Monthly return limits are enforced to prevent excessive payouts</li>
              <li>• TH/s ranges should not overlap for proper calculation</li>
              <li>• Daily returns are randomly selected within the specified range for each unit</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    );
  }

  function renderBinaryTab() {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <ArrowUpDown className="h-5 w-5 text-purple-400" />
            Binary Matching Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Max Points Per Side
              </label>
              <Input
                type="number"
                value={settings.maxBinaryPointsPerSide}
                onChange={(e) => updateSetting('maxBinaryPointsPerSide', parseInt(e.target.value) || 0)}
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Point Value (USD)
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.binaryPointValue}
                  onChange={(e) => updateSetting('binaryPointValue', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Matching Schedule
              </label>
              <Input
                type="text"
                value={settings.binaryMatchingSchedule}
                onChange={(e) => updateSetting('binaryMatchingSchedule', e.target.value)}
                placeholder="Weekly at 15:00 UTC"
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-purple-500"
              />
            </div>
          </div>

          <div className="flex items-center justify-between pt-4 border-t border-slate-600">
            <div>
              <h4 className="text-sm font-medium text-white">Binary Matching Enabled</h4>
              <p className="text-sm text-slate-400">Enable automatic binary point matching</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.binaryMatchingEnabled}
                onChange={(e) => updateSetting('binaryMatchingEnabled', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>

          <div className="mt-4 p-4 bg-slate-700 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h4 className="text-sm font-medium text-slate-300 mb-2">Manual Binary Matching</h4>
                <p className="text-xs text-slate-400">Trigger binary matching process manually</p>
              </div>
              <Button
                onClick={handleManualBinaryMatching}
                loading={processingMatching}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Process Matching
              </Button>
            </div>

            <h4 className="text-sm font-medium text-slate-300 mb-2">Binary Matching Rules:</h4>
            <ul className="text-xs text-slate-400 space-y-1">
              <li>• Points are matched weekly at the scheduled time</li>
              <li>• Matched points are paid out at the configured point value</li>
              <li>• Excess points beyond the maximum are reset (pressure out)</li>
              <li>• Only active users (with mining units) receive binary points</li>
              <li>• Manual matching can be triggered anytime by admin</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    );
  }

  function renderDepositsTab() {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Wallet className="h-5 w-5 text-green-400" />
            Deposit Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                USDT TRC20 Deposit Address
              </label>
              <Input
                type="text"
                value={settings.usdtDepositAddress}
                onChange={(e) => updateSetting('usdtDepositAddress', e.target.value)}
                placeholder="Enter USDT TRC20 address (e.g., TXXXxxxXXXxxxXXX...)"
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500 font-mono text-sm"
              />
              <p className="text-xs text-slate-400 mt-1">
                This address will be displayed to users for USDT deposits
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Minimum Deposit
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.minDepositAmount}
                  onChange={(e) => updateSetting('minDepositAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Maximum Deposit
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.maxDepositAmount}
                  onChange={(e) => updateSetting('maxDepositAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Deposit Fee (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.depositFeePercentage}
                  onChange={(e) => updateSetting('depositFeePercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Minimum Confirmations
              </label>
              <Input
                type="number"
                value={settings.minConfirmations}
                onChange={(e) => updateSetting('minConfirmations', parseInt(e.target.value) || 0)}
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-green-500"
              />
              <p className="text-xs text-slate-400 mt-1">
                Number of blockchain confirmations required
              </p>
            </div>
            <div className="flex items-center justify-between pt-6">
              <div>
                <h4 className="text-sm font-medium text-white">Deposits Enabled</h4>
                <p className="text-sm text-slate-400">Allow users to make deposits</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.depositEnabled}
                  onChange={(e) => updateSetting('depositEnabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
              </label>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  function renderWithdrawalsTab() {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <DollarSign className="h-5 w-5 text-red-400" />
            Withdrawal Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Minimum Withdrawal
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.minWithdrawalAmount}
                  onChange={(e) => updateSetting('minWithdrawalAmount', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Fixed Fee ($)
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.withdrawalFeeFixed}
                  onChange={(e) => updateSetting('withdrawalFeeFixed', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Percentage Fee (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.withdrawalFeePercentage}
                  onChange={(e) => updateSetting('withdrawalFeePercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Processing Days
              </label>
              <Input
                type="number"
                value={settings.withdrawalProcessingDays}
                onChange={(e) => updateSetting('withdrawalProcessingDays', parseInt(e.target.value) || 0)}
                className="bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-red-500"
              />
            </div>
          </div>

          <div className="mt-4 p-4 bg-slate-700 rounded-lg">
            <h4 className="text-sm font-medium text-slate-300 mb-2">Fee Calculation Example:</h4>
            <p className="text-xs text-slate-400">
              For a $100 withdrawal: Fixed Fee (${settings.withdrawalFeeFixed}) + Percentage Fee (${(100 * (settings.withdrawalFeePercentage / 100)).toFixed(2)}) = Total Fee: ${(settings.withdrawalFeeFixed + (100 * (settings.withdrawalFeePercentage / 100))).toFixed(2)}
            </p>
            <p className="text-xs text-slate-400 mt-1">
              User receives: ${(100 - (settings.withdrawalFeeFixed + (100 * (settings.withdrawalFeePercentage / 100)))).toFixed(2)}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  function renderPlatformTab() {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Globe className="h-5 w-5 text-blue-400" />
            Platform Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-2">
                Platform Fee (%)
              </label>
              <div className="relative">
                <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="number"
                  step="0.01"
                  value={settings.platformFeePercentage}
                  onChange={(e) => updateSetting('platformFeePercentage', parseFloat(e.target.value) || 0)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4 pt-4 border-t border-slate-600">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-white">Maintenance Mode</h4>
                <p className="text-sm text-slate-400">Temporarily disable platform access</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.maintenanceMode}
                  onChange={(e) => updateSetting('maintenanceMode', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-white">Registration Enabled</h4>
                <p className="text-sm text-slate-400">Allow new user registrations</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.registrationEnabled}
                  onChange={(e) => updateSetting('registrationEnabled', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-sm font-medium text-white">KYC Required</h4>
                <p className="text-sm text-slate-400">Require KYC verification for withdrawals</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.kycRequired}
                  onChange={(e) => updateSetting('kycRequired', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
};
