import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import {
  getBinaryTreeStructure,
  getDetailedTeamStats,
  getTreeHealthStats,
  getTotalTeamCount,
  loadNodeChildren
} from '@/lib/referral';
import { referralDb, binaryPointsDb, transactionDb } from '@/lib/database';
import { prisma } from '@/lib/prisma';

// GET - Fetch user's enhanced binary tree structure
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const depth = parseInt(searchParams.get('depth') || '3');
    const enhanced = searchParams.get('enhanced') === 'true';
    const expandedNodes = searchParams.get('expanded') ?
      new Set(searchParams.get('expanded')!.split(',').filter(id => id.length > 0)) :
      new Set<string>();
    const nodeId = searchParams.get('nodeId'); // For loading specific node children

    // If requesting specific node children
    if (nodeId) {
      const children = await loadNodeChildren(nodeId);
      return NextResponse.json({
        success: true,
        data: children,
      });
    }

    // Get binary tree structure with expanded nodes
    const treeStructure = await getBinaryTreeStructure(user.id, Math.min(depth, 15), expandedNodes); // Increased max depth

    // Get user's referral statistics - use user table for direct referrals (sponsor relationships)
    const directReferralsFromUserTable = await prisma.user.findMany({
      where: { referrerId: user.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        isActive: true,
        createdAt: true,
      },
    });

    // Get binary tree placement data from referral table
    const binaryPlacements = await referralDb.findByReferrerId(user.id);
    const binaryPoints = await binaryPointsDb.findByUserId(user.id);

    // Calculate placement statistics
    const leftPlacements = binaryPlacements.filter(r => r.placementSide === 'LEFT').length;
    const rightPlacements = binaryPlacements.filter(r => r.placementSide === 'RIGHT').length;

    // Get total team counts (including all downliners)
    const teamCounts = await getTotalTeamCount(user.id);

    // Calculate total commissions from transactions
    const commissionTransactions = await transactionDb.findByUserId(user.id, {
      types: ['DIRECT_REFERRAL', 'BINARY_BONUS'],
      status: 'COMPLETED',
    });
    const totalCommissions = commissionTransactions.reduce((sum, t) => sum + t.amount, 0);

    let responseData: any = {
      treeStructure,
      statistics: {
        totalDirectReferrals: directReferralsFromUserTable.length,
        leftPlacements,
        rightPlacements,
        leftReferrals: teamCounts.left, // Use team counts for left/right referrals display
        rightReferrals: teamCounts.right,
        leftTeam: teamCounts.left,
        rightTeam: teamCounts.right,
        totalTeam: teamCounts.total,
        totalCommissions,
        binaryPoints: binaryPoints || { leftPoints: 0, rightPoints: 0, matchedPoints: 0 },
      },
      referralLinks: {
        left: `${process.env.NEXT_PUBLIC_APP_URL}/register?ref=${user.referralId}&side=left`,
        right: `${process.env.NEXT_PUBLIC_APP_URL}/register?ref=${user.referralId}&side=right`,
        general: `${process.env.NEXT_PUBLIC_APP_URL}/register?ref=${user.referralId}`,
      },
    };

    // Add enhanced statistics if requested
    if (enhanced) {
      const detailedStats = await getDetailedTeamStats(user.id);
      const treeHealth = await getTreeHealthStats(user.id);

      responseData.statistics.detailedStats = detailedStats;
      responseData.statistics.treeHealth = treeHealth;
    }

    return NextResponse.json({
      success: true,
      data: responseData,
    });

  } catch (error: any) {
    console.error('Binary tree fetch error:', error);

    return NextResponse.json(
      { success: false, error: 'Failed to fetch binary tree' },
      { status: 500 }
    );
  }
}
